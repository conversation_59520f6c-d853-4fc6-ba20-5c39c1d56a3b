<template>
  <div class="comment">
    <!-- Child comment spacer -->
    <div v-if="comment.parentCommentId" class="comment-spacer">

    </div>
    <!-- Profile pic -->
    <q-avatar class="cursor-pointer" @click.stop="onClickAvatar">
      <q-badge v-if="comment.childComments" floating color="red">{{ comment.childComments.length }}</q-badge>
      <img v-if="comment.profile.profileImage" :src="comment.profile.profileImage" loading="lazy" decoding="async">
      <div v-else class="profile-none"></div>
      <!-- User info popup -->
      <q-tooltip class="comment-user-details" persistent
        anchor="top middle" self="bottom middle" :offset="[0, 10]"
        :delay="250" :hideDelay="1000"
        @beforeShow="fetchUserDetails(comment.profile.proxyWallet)"
      >
        <div v-if="userHoverDetails" class="column items-start q-pa-sm">
          <div class="text-small text-grey-8 wallet"
            @click="onClickUserDetailsWallet"
          >
            {{ comment.profile.proxyWallet }}
          </div>
          <div class="text-body2">
            {{  `Pnl: ${formatCurrency(userHoverDetails.profit, 0)}` }}
          </div>
          <div class="comment-positions">
            <div v-for="pos of userHoverDetails.positions" class="text-caption text-medium" :class="pos.outcomeIndex === 0 ? 'bg-yes text-yes' : 'bg-no text-no'">
              <b>{{ eventStore.marketLookupByCondId[pos.conditionId.toLowerCase()]?.shortName || pos.outcome }}</b>: {{ formatDecimal(pos.size, 0) }}
            </div>
          </div>
        </div>
        <div v-else class="column items-center">
          <q-spinner-dots
            size="20px"
            color="primary"
            class="q-my-sm"
          />
        </div>
      </q-tooltip>
    </q-avatar>
    <!-- Comment body -->
    <div @click="onClickComment(props.comment)" class="comment-main cursor-pointer">
      <div class="comment-info">
        <b class="username" @click.stop="onClickUsername">{{ comment.profile.name }}</b>&nbsp;
        <!-- Heart rection -->
        <div @mouseenter="onHeartHoverStart" @mouseleave="onHeartHoverEnd" class="heart-icon">
          <svg @click.stop="onClickHeartReact" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path v-if="!isHearted" d="M10 3.82916C9.09203 2.99424 7.90353 2.53086 6.67002 2.53082C6.01714 2.5315 5.37084 2.66129 4.76831 2.91271C4.16577 3.16414 3.61891 3.53223 3.15919 3.99582C1.19836 5.96499 1.19919 9.04499 3.16086 11.0058L9.27086 17.1158C9.41253 17.365 9.68586 17.5258 10 17.5258C10.129 17.5246 10.2559 17.4931 10.3706 17.4339C10.4852 17.3747 10.5843 17.2894 10.66 17.185L16.8392 11.0058C18.8009 9.04416 18.8009 5.96499 16.8375 3.99249C16.378 3.52975 15.8316 3.1624 15.2297 2.91156C14.6277 2.66071 13.9821 2.53132 13.33 2.53082C12.0965 2.53102 10.9081 2.99438 10 3.82916ZM15.6592 5.17082C16.9617 6.47999 16.9625 8.52499 15.6609 9.82749L10 15.4883L4.33919 9.82749C3.03752 8.52499 3.03836 6.47999 4.33752 5.17416C4.97086 4.54416 5.79919 4.19749 6.67002 4.19749C7.54086 4.19749 8.36586 4.54416 8.99419 5.17249L9.41086 5.58916C9.48818 5.66661 9.58002 5.72806 9.68111 5.76998C9.78221 5.81191 9.89058 5.83349 10 5.83349C10.1095 5.83349 10.2178 5.81191 10.3189 5.76998C10.42 5.72806 10.5119 5.66661 10.5892 5.58916L11.0059 5.17249C12.2659 3.91499 14.4009 3.91832 15.6592 5.17082Z" fill="#828282"></path>
            <path v-if="isHearted" d="M16.8375 3.99249C16.378 3.52975 15.8316 3.1624 15.2297 2.91156C14.6277 2.66071 13.9821 2.53132 13.33 2.53082C12.0965 2.53102 10.9081 2.99438 10 3.82916C9.09203 2.99424 7.90353 2.53086 6.67002 2.53082C6.01714 2.5315 5.37084 2.66129 4.76831 2.91271C4.16577 3.16414 3.61891 3.53223 3.15919 3.99582C1.19836 5.96499 1.19919 9.04499 3.16086 11.0058L10 17.845L16.8392 11.0058C18.8009 9.04499 18.8017 5.96499 16.8375 3.99249Z" fill="#F55A00"></path>
          </svg>
          <q-menu v-model="showHeartUsers" anchor="top middle" self="bottom left" class="heart-users">
            <div v-for="r of comment.reactions">{{ polyUser.usernameCache[r.userAddress] || (polyUser.fetchUsernameIfNotCached(r.userAddress), r.userAddress) }}</div>
          </q-menu>
        </div>
        <!-- # heart reactions -->
        <span v-if="totalReactions">{{ totalReactions }}</span>
        <!-- Delete comment button -->
        <q-icon v-if="comment.userAddress.toLowerCase() == user.storage.baseAddress"
          @click.stop="onClickDeleteComment"
          name="close" class="cursor-pointer text-no delete-icon" size="xs"
        />
        <!-- Largest position -->
        <span v-if="largestPosition" class="largest-position" :class="largestPosition.isOutcomeA ? 'outcome-a' : 'outcome-b'">
          <b>{{ largestPosition.market.shortName ? largestPosition.market.shortName + ':' : largestPosition.outcomeName }}</b> {{ formatDecimal(largestPosition.shares, 0) }}
        </span>&nbsp;
        <!-- Time posted -->
        <span class="comment-time">{{ `${formatTimeAgoShort(comment.createdAt, 1, curTime)} ago` }}</span>
      </div>
      <!-- Comment text -->
      <div class="comment-text">
        <span v-if="replyName" class="reply-name">{{ `@${replyName} ` }}</span>
        <span v-html="commentTextProcessed"></span>
      </div>
    </div>
  </div>
  <Comment v-if="isExpanded" v-for="c of comment.childComments"
    :key="c.id"
    :comment="c"
    @click="onClickComment(c)"
  />
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import { formatTimeAgoShort, filterMap, formatCurrency, formatDecimal, linkifyText } from "src/utils";
import { PolyDataComment, PolyReactionType } from "@shared/api-dataclasses-shared";
import { useUserStore } from "src/stores/user-store";
import { useEventStore } from "src/stores/event-store";
import { usePolyUserStore } from "src/stores/polyuser-store";
import { useAppStore } from "src/stores/app-store";

const props = defineProps<{
  comment: PolyDataComment;
  curTime?: number; //Allows parent to update '5m ago' comment time display
}>();
const emit = defineEmits(['click']);

const heartHoverDelay = 500;

const eventStore = useEventStore();
const user = useUserStore();
const polyUser = usePolyUserStore();
const appStore = useAppStore();

//Refs

const isExpanded = ref(false);
const totalReactions = ref(props.comment.reactions.length);
const ourReactionIds = ref<{id: string, type: PolyReactionType}[]>(
  filterMap(props.comment.reactions, (r => r.userAddress.toLowerCase() === user.storage.baseAddress ? {id: r.id, type: r.reactionType as any} : undefined))
);
const isHearted = ref(ourReactionIds.value.length > 0);
const showHeartUsers = ref(false);
const userHoverDetails = ref<Awaited<ReturnType<typeof fetchUserDetails>> | null>(null);

const replyName = computed(() => {
  if (!props.comment.replyAddress) return;
  return eventStore.commentLookup.profileByAddress[props.comment.replyAddress]?.name || "";
});

const commentTextProcessed = computed(() => {
  return linkifyText(props.comment.text);
});

const largestPosition = computed(() => {
  if (!props.comment.profile.positions || props.comment.profile.positions.length === 0) {
    return null;
  }

  //Find the position with the largest share count that belongs to the current event
  let largestPos = null;
  let largestShares = 0;

  for (const position of props.comment.profile.positions) {
    const currentShares = Number(position.positionSize) / 1000000; //Divide by 1,000,000 as per comment in interface

    //Only consider positions that belong to markets in the current event
    const market = eventStore.marketLookupByAssetId[position.tokenId];
    if (market && currentShares > largestShares) {
      largestPos = position;
      largestShares = currentShares;
    }
  }

  //If no position found for current event, return null
  if (!largestPos) {
    return null;
  }

  //Look up the market and determine if it's outcome A or B
  const market = eventStore.marketLookupByAssetId[largestPos.tokenId];
  if (!market) {
    return null;
  }

  const isOutcomeA = largestPos.tokenId === market.assetIdA;
  const shares = Number(largestPos.positionSize) / 1000000;
  const outcomeName = isOutcomeA ? market.outcomeNameA : market.outcomeNameB;

  return {
    shares,
    market,
    isOutcomeA,
    outcomeName
  };
});

async function fetchUserDetails(userProxyWallet: string) {
  userHoverDetails.value = null;
  const [ proft, positions ] = await Promise.all([
    polyUser.fetchUserProfit(userProxyWallet),
    polyUser.fetchUserPositions(userProxyWallet, eventStore.event.markets.map(m => m.conditionId)),
  ]);

  const ret = {
    profit: proft,
    positions: positions.sort((a, b) => eventStore.marketLookupByCondId[a.conditionId]!.sortOrder - eventStore.marketLookupByCondId[b.conditionId]!.sortOrder),
    proxyWallet: userProxyWallet
  };
  userHoverDetails.value = ret;
  return ret;
}

function onClickComment(comment: PolyDataComment) {
  if (comment == props.comment) {
    isExpanded.value = !isExpanded.value;
  }
  emit("click", comment);
}

async function onClickHeartReact() {
  if (!isHearted.value) {
    try {
      const reactData = await eventStore.postCommentReaction(props.comment.id, "HEART");
      ourReactionIds.value.push({id: reactData.id, type: "HEART"});
      isHearted.value = true;
      totalReactions.value++;
    }
    catch (e) {
    }
  }
  else {
    try {
      const ourHeartId = ourReactionIds.value.findIndex(reaction => reaction.type === "HEART");
      if (ourHeartId === -1) {
        throw new Error("isHearted is set but no heart reaction found");
      }

      await eventStore.deleteCommentReaction(ourReactionIds.value[ourHeartId].id);
      ourReactionIds.value.splice(ourHeartId, 1);
      isHearted.value = false;
      totalReactions.value--;
    }
    catch (e) {
    }
  }
}

async function onClickDeleteComment() {
  await eventStore.deleteComment(props.comment);
}

async function onClickUserDetailsWallet() {
  navigator.clipboard.writeText(userHoverDetails.value!.proxyWallet);
}

function onClickUsername() {
  appStore.showUserPositionsDialog(props.comment.profile.proxyWallet);
}

function onClickAvatar() {
  appStore.showUserPositionsDialog(props.comment.profile.proxyWallet);
}

let timerHeartHover = -1;
function onHeartHoverStart() {
  if (timerHeartHover === -1) {
    timerHeartHover = setTimeout(() => {
      showHeartUsers.value = true;
      timerHeartHover = -1;
    }, heartHoverDelay) as unknown as number;
  }
}

function onHeartHoverEnd() {
  showHeartUsers.value = false;
  if (timerHeartHover !== -1) {
    clearTimeout(timerHeartHover);
    timerHeartHover = -1;
  }
}
</script>

<style scoped lang="scss">
.comment {
  padding: 6px;
  border-bottom: 1px solid #ccc;
  overflow-wrap: anywhere;
  display: flex;

  .comment-main {
    display: flex;
    flex-direction: column;
    padding-left: 12px;
    flex-grow: 1;
  }

  .comment-info {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
  }

  .comment-text {
    flex: 1 1 auto;
  }

  .comment-time {
    font-size: 10px;
    color: #999;
    margin-left: 6px;
  }

  .largest-position {
    font-size: 12px;
    margin-left: 5px;
    padding: 2px 4px;
    border-radius: 3px;

    &.outcome-a {
      color: $YesPrimary;
      background-color: $YesBackground;
    }

    &.outcome-b {
      color: $NoPrimary;
      background-color: $NoBackground;
    }
  }

  .comment-spacer {
    width: 48px;
    min-width: 48px;
  }

  img {
    object-fit: cover;
    width: 48px;
    min-width: 48px;
    height: 48px;
  }

  .profile-none {
    background-color: #333333;
    border-radius: 50%;
    width: 48px;
    min-width: 48px;
    height: 48px;
  }

  .heart-icon {
    margin-left: 6px;
    display: flex;
  }

  .delete-icon {
    margin-left: 6px;
  }

  .reply-name {
    font-weight: bold;
    color: #1976D2;
  }

  .username {
    cursor: pointer;
    max-width: 20ch;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inline-block;

    &:hover {
      color: #1976D2;
    }
  }
}
</style>

<style>
.heart-users {
  padding: 6px;
  border: 1px dashed lightgrey;

  > div {
    font-weight: 500;
    margin-top: 2px;
  }

  > div:first-child {
    margin-top: 0;
  }
}

.comment-user-details {
    background-color: #ffffff;
    color: black;
    border: 1px solid black;
    pointer-events: all !important;
    overflow: hidden !important;

    > * {
      min-width: 95;
    }

    .wallet {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      max-width: 95px;
      cursor: pointer;
    }

    .comment-positions {
      display: flex;
      flex-direction: column;
      flex-wrap: wrap;
      align-content: flex-start;
      max-height: 105px;
      overflow: hidden;

      > * {
        padding: 0px 4px 0px 4px;
      }
    }
  }
</style>
